"use client";

import React, { useState, useEffect } from "react";
import { X, Save, Loader2 } from "lucide-react";
import confetti from "canvas-confetti";
import { getWeekInfo, canAddDataForWeek, getWeekDataRestrictionMessage, getLastCompletedWeekInfo, type WeekInfo } from "@/lib/utils/weekUtils";
import { type KpiSemanalData } from "@/app/actions/kpis-semanales";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import AlertDialog from "@/components/ui/AlertDialog";

// Interfaz para el formulario que permite valores string o number
interface FormKpiData extends Omit<KpiSemanalData, 'volumenTotalLitros' | 'crecimientoMensual' | 'margenBrutoPorLitro' | 'tasaRetencionClientes' | 'cumplimientoObjetivo' | 'desviacionVentas' | 'cicloPromedioCierre' | 'clientesActivosMensuales'> {
  volumenTotalLitros: number | string;
  crecimientoMensual: number | string;
  margenBrutoPorLitro: number | string;
  tasaRetencionClientes: number | string;
  cumplimientoObjetivo: number | string;
  desviacionVentas: number | string;
  cicloPromedioCierre: number | string;
  clientesActivosMensuales: number | string;
}

// Componente Tooltip personalizado estilo Chart.js
const Tooltip: React.FC<{ children: React.ReactNode; content: string; className?: string }> = ({
  children,
  content,
  className = ""
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: true, left: false, right: false, center: true });
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const updatePosition = React.useCallback(() => {
    if (!containerRef.current || !tooltipRef.current) return;

    const container = containerRef.current.getBoundingClientRect();
    const tooltip = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Determinar posición vertical (arriba o abajo)
    const spaceBelow = viewport.height - container.bottom;
    const spaceAbove = container.top;
    const tooltipHeight = tooltip.height || 80; // altura estimada más realista

    const showAbove = spaceBelow < tooltipHeight + 20 && spaceAbove > tooltipHeight + 20;

    // Determinar posición horizontal (centrado, izquierda o derecha)
    const containerCenter = container.left + container.width / 2;
    const tooltipWidth = tooltip.width || 280; // ancho estimado más realista
    const halfTooltipWidth = tooltipWidth / 2;
    const margin = 20; // margen de seguridad

    let horizontalPosition = 'center';
    if (containerCenter - halfTooltipWidth < margin) {
      horizontalPosition = 'left';
    } else if (containerCenter + halfTooltipWidth > viewport.width - margin) {
      horizontalPosition = 'right';
    }

    setPosition({
      top: !showAbove,
      left: horizontalPosition === 'left',
      right: horizontalPosition === 'right',
      center: horizontalPosition === 'center'
    });
  }, []);

  React.useEffect(() => {
    if (isVisible) {
      updatePosition();
    }
  }, [isVisible, updatePosition]);

  const getTooltipClasses = () => {
    let classes = "absolute px-3 py-2 text-xs font-medium text-white rounded-md shadow-lg pointer-events-none transition-opacity duration-200";

    if (position.top) {
      classes += " top-full mt-2";
    } else {
      classes += " bottom-full mb-2";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-0";
    } else if (position.right) {
      classes += " right-0";
    }

    return classes;
  };

  const getArrowClasses = () => {
    let classes = "absolute w-0 h-0";

    if (position.top) {
      classes += " bottom-full";
    } else {
      classes += " top-full";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-3";
    } else if (position.right) {
      classes += " right-3";
    }

    return classes;
  };

  const getArrowStyle = () => {
    const baseStyle = {
      borderLeft: '6px solid transparent',
      borderRight: '6px solid transparent',
    };

    if (position.top) {
      return {
        ...baseStyle,
        borderBottom: '6px solid rgba(0, 0, 0, 0.8)'
      };
    } else {
      return {
        ...baseStyle,
        borderTop: '6px solid rgba(0, 0, 0, 0.8)'
      };
    }
  };

  return (
    <div
      ref={containerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(4px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            maxWidth: '280px',
            width: 'max-content',
            whiteSpace: 'normal',
            wordWrap: 'break-word',
            lineHeight: '1.4'
          }}
        >
          {content}
          <div
            className={getArrowClasses()}
            style={getArrowStyle()}
          ></div>
        </div>
      )}
    </div>
  );
};

interface KpiSemanalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: KpiSemanalData) => Promise<void>;
  editingKpi?: KpiSemanalData | null;
  isAddingOldWeek?: boolean;
  existingKpis?: KpiSemanalData[];
}

const KpiSemanalModal: React.FC<KpiSemanalModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingKpi,
  isAddingOldWeek = false,
  existingKpis = []
}) => {
  const [loading, setLoading] = useState(false);
  const [currentWeek, setCurrentWeek] = useState<WeekInfo | null>(null);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [showDuplicateAlert, setShowDuplicateAlert] = useState(false);
  const [showWeekRestrictionAlert, setShowWeekRestrictionAlert] = useState(false);
  const [weekRestrictionMessage, setWeekRestrictionMessage] = useState("");
  const [formData, setFormData] = useState<FormKpiData>({
    year: new Date().getFullYear(),
    weekNumber: 1,
    weekStartDate: "",
    weekEndDate: "",
    volumenTotalLitros: "" as any,
    crecimientoMensual: "" as any,
    margenBrutoPorLitro: "" as any,
    tasaRetencionClientes: "" as any,
    cumplimientoObjetivo: "" as any,
    desviacionVentas: "" as any,
    cicloPromedioCierre: "" as any,
    clientesActivosMensuales: "" as any
  });

  useEffect(() => {
    if (isOpen) {
      if (editingKpi) {
        setFormData(editingKpi);
        const weekInfo = getWeekInfo(editingKpi.year, editingKpi.weekNumber);
        setCurrentWeek(weekInfo);
        setSelectedYear(editingKpi.year);
        setSelectedWeek(editingKpi.weekNumber);
      } else if (isAddingOldWeek) {
        // Para semanas antiguas, inicializar con valores por defecto pero permitir selección
        const currentYear = new Date().getFullYear();
        setSelectedYear(currentYear);
        setSelectedWeek(1);
        try {
          const weekInfo = getWeekInfo(currentYear, 1);
          setCurrentWeek(weekInfo);
          setFormData({
            year: currentYear,
            weekNumber: 1,
            weekStartDate: weekInfo.startDate.toISOString(),
            weekEndDate: weekInfo.endDate.toISOString(),
            volumenTotalLitros: "" as any,
            crecimientoMensual: "" as any,
            margenBrutoPorLitro: "" as any,
            tasaRetencionClientes: "" as any,
            cumplimientoObjetivo: "" as any,
            desviacionVentas: "" as any,
            cicloPromedioCierre: "" as any,
            clientesActivosMensuales: "" as any
          });
        } catch (error) {
          console.error("Error al inicializar semana antigua:", error);
        }
      } else {
        // Para nueva entrada de datos, usar la última semana completada
        try {
          const weekInfo = getLastCompletedWeekInfo();
          setCurrentWeek(weekInfo);
          setSelectedYear(weekInfo.year);
          setSelectedWeek(weekInfo.weekNumber);
          setFormData({
            year: weekInfo.year,
            weekNumber: weekInfo.weekNumber,
            weekStartDate: weekInfo.startDate.toISOString(),
            weekEndDate: weekInfo.endDate.toISOString(),
            volumenTotalLitros: "" as any,
            crecimientoMensual: "" as any,
            margenBrutoPorLitro: "" as any,
            tasaRetencionClientes: "" as any,
            cumplimientoObjetivo: "" as any,
            desviacionVentas: "" as any,
            cicloPromedioCierre: "" as any,
            clientesActivosMensuales: "" as any
          });
        } catch (error) {
          console.error("Error al inicializar última semana completada:", error);
        }
      }
    }
  }, [isOpen, editingKpi, isAddingOldWeek]);

  const handleInputChange = (field: keyof FormKpiData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Función para manejar cambio de año
  const handleYearChange = (year: number) => {
    // Validar que year sea un número válido
    if (isNaN(year) || year < 1900 || year > 2100) {
      console.error("Año inválido:", year);
      return;
    }

    setSelectedYear(year);
    try {
      const weekInfo = getWeekInfo(year, selectedWeek);
      setCurrentWeek(weekInfo);
      setFormData(prev => ({
        ...prev,
        year: year,
        weekStartDate: weekInfo.startDate.toISOString(),
        weekEndDate: weekInfo.endDate.toISOString()
      }));
    } catch (error) {
      console.error("Error al obtener información de la semana:", error);
      // Usar semana 1 como fallback
      const fallbackWeekInfo = getWeekInfo(year, 1);
      setSelectedWeek(1);
      setCurrentWeek(fallbackWeekInfo);
      setFormData(prev => ({
        ...prev,
        year: year,
        weekNumber: 1,
        weekStartDate: fallbackWeekInfo.startDate.toISOString(),
        weekEndDate: fallbackWeekInfo.endDate.toISOString()
      }));
    }
  };

  // Función para manejar cambio de semana
  const handleWeekChange = (week: number) => {
    // Validar que week sea un número válido
    if (isNaN(week) || week < 1 || week > 53) {
      console.error("Número de semana inválido:", week);
      return;
    }

    setSelectedWeek(week);
    try {
      const weekInfo = getWeekInfo(selectedYear, week);
      setCurrentWeek(weekInfo);
      setFormData(prev => ({
        ...prev,
        weekNumber: week,
        weekStartDate: weekInfo.startDate.toISOString(),
        weekEndDate: weekInfo.endDate.toISOString()
      }));
    } catch (error) {
      console.error("Error al obtener información de la semana:", error);
      // Usar semana 1 como fallback
      const fallbackWeekInfo = getWeekInfo(selectedYear, 1);
      setSelectedWeek(1);
      setCurrentWeek(fallbackWeekInfo);
      setFormData(prev => ({
        ...prev,
        weekNumber: 1,
        weekStartDate: fallbackWeekInfo.startDate.toISOString(),
        weekEndDate: fallbackWeekInfo.endDate.toISOString()
      }));
    }
  };

  // Función para verificar si ya existe un KPI para la semana seleccionada
  const checkDuplicateKpi = (year: number, weekNumber: number): boolean => {
    if (editingKpi) return false; // Si estamos editando, no verificar duplicados

    return existingKpis.some(kpi =>
      kpi.year === year && kpi.weekNumber === weekNumber
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Verificar si ya existe un KPI para esta semana
    if (checkDuplicateKpi(formData.year, formData.weekNumber)) {
      setShowDuplicateAlert(true);
      return;
    }

    // Verificar si se puede agregar datos para esta semana (solo para nuevos KPIs, no para edición)
    if (!editingKpi && !canAddDataForWeek(formData.year, formData.weekNumber)) {
      setWeekRestrictionMessage(getWeekDataRestrictionMessage(formData.year, formData.weekNumber));
      setShowWeekRestrictionAlert(true);
      return;
    }

    setLoading(true);

    try {
      // Convertir valores vacíos a 0 antes de enviar
      const processedData: KpiSemanalData = {
        ...formData,
        volumenTotalLitros: formData.volumenTotalLitros === "" || formData.volumenTotalLitros === null ? 0 : Number(formData.volumenTotalLitros),
        crecimientoMensual: formData.crecimientoMensual === "" || formData.crecimientoMensual === null ? 0 : Number(formData.crecimientoMensual),
        margenBrutoPorLitro: formData.margenBrutoPorLitro === "" || formData.margenBrutoPorLitro === null ? 0 : Number(formData.margenBrutoPorLitro),
        tasaRetencionClientes: formData.tasaRetencionClientes === "" || formData.tasaRetencionClientes === null ? 0 : Number(formData.tasaRetencionClientes),
        cumplimientoObjetivo: formData.cumplimientoObjetivo === "" || formData.cumplimientoObjetivo === null ? 0 : Number(formData.cumplimientoObjetivo),
        desviacionVentas: formData.desviacionVentas === "" || formData.desviacionVentas === null ? 0 : Number(formData.desviacionVentas),
        cicloPromedioCierre: formData.cicloPromedioCierre === "" || formData.cicloPromedioCierre === null ? 0 : Number(formData.cicloPromedioCierre),
        clientesActivosMensuales: formData.clientesActivosMensuales === "" || formData.clientesActivosMensuales === null ? 0 : Number(formData.clientesActivosMensuales)
      };

      await onSave(processedData);

      // Lanzar confeti después de guardar exitosamente
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });

      onClose();
    } catch (error) {
      console.error("Error al guardar KPI:", error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const kpiFields = [
    {
      key: "volumenTotalLitros" as keyof FormKpiData,
      title: "VOLUMEN TOTAL DE VENTA POR MES (L)",
      label: "Volumen Total de Venta por Mes (Litros)",
      description: "Suma total de litros vendidos en el mes",
      type: "number",
      min: 0,
      max: 100000000,
      step: 1
    },
    {
      key: "crecimientoMensual" as keyof FormKpiData,
      title: "CRECIMIENTO MENSUAL DE VENTAS (%)",
      label: "Crecimiento Mensual de Ventas (%)",
      description: "[(Ventas mes actual - Ventas mes anterior) / Ventas mes anterior] * 100",
      type: "number",
      min: -100000000,
      max: 100000000,
      step: 0.1
    },
    {
      key: "margenBrutoPorLitro" as keyof FormKpiData,
      title: "MARGEN BRUTO POR LITRO VENDIDO",
      label: "Margen Bruto por Litro Vendido",
      description: "(Ingreso total - Costo total) / Litros vendidos",
      type: "number",
      min: 0,
      max: 100000000,
      step: 0.01
    },
    {
      key: "tasaRetencionClientes" as keyof FormKpiData,
      title: "TASA DE RETENCIÓN DE CLIENTES (%)",
      label: "Tasa de Retención de Clientes (%)",
      description: "(Clientes retenidos / Clientes del periodo anterior) * 100",
      type: "number",
      min: 0,
      max: 100,
      step: 0.1
    },
    {
      key: "cumplimientoObjetivo" as keyof FormKpiData,
      title: "CUMPLIMIENTO DEL OBJETIVO DE VENTAS (%)",
      label: "Porcentaje de Cumplimiento del Objetivo de Ventas Mensual (%)",
      description: "(Ventas reales / Meta de ventas) * 100",
      type: "number",
      min: 0,
      max: 200,
      step: 0.1
    },
    {
      key: "desviacionVentas" as keyof FormKpiData,
      title: "DESVIACIÓN VENTAS PROYECTADAS VS REALES (%)",
      label: "Desviación entre Ventas Proyectadas y Reales (%)",
      description: "(Ventas reales - Ventas proyectadas) / Ventas proyectadas * 100",
      type: "number",
      min: -100000000,
      max: 100000000,
      step: 0.1
    },
    {
      key: "cicloPromedioCierre" as keyof FormKpiData,
      title: "CICLO PROMEDIO DE CIERRE DE VENTAS (DÍAS)",
      label: "Ciclo Promedio de Cierre de Ventas (Días)",
      description: "Promedio de días entre contacto inicial y cierre",
      type: "number",
      min: 0,
      max: 100000000,
      step: 1
    },
    {
      key: "clientesActivosMensuales" as keyof FormKpiData,
      title: "CLIENTES ACTIVOS MENSUALES",
      label: "Número de Clientes Activos Mensuales",
      description: "Clientes con al menos una compra en el mes",
      type: "number",
      min: 0,
      max: 100000000,
      step: 1
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-50 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between px-6 pt-5 flex-shrink-0 rounded-t-lg">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {editingKpi ? "Editar" : isAddingOldWeek ? "Agregar Semana Antigua" : "Agregar"} Datos Semanales
            </h2>
            {currentWeek && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-blue-800 mt-1">
                {currentWeek.label}
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="flex flex-col flex-1 min-h-0">
          <div className="p-6 space-y-6 overflow-y-auto flex-1">
            {/* Selectores de Año y Semana para semanas antiguas */}
            {isAddingOldWeek && !editingKpi && (
              <div className="bg-primary/10 rounded-lg p-3">
                <h3 className="text-sm font-medium text-blue-800 mb-2">Seleccionar Semana</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="flex flex-col h-full">
                    <label className="block text-sm font-medium text-blue-800 mb-1">
                      Año
                    </label>
                    <Select
                      value={selectedYear.toString()}
                      onValueChange={(value) => {
                        const year = parseInt(value);
                        if (!isNaN(year)) {
                          handleYearChange(year);
                        }
                      }}
                    >
                      <SelectTrigger className="w-full border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent h-10">
                        <SelectValue placeholder="Seleccione un año" />
                      </SelectTrigger>
                      <SelectContent
                        position="popper"
                        side="bottom"
                        align="start"
                        sideOffset={4}
                        className="z-[9999] max-h-[200px] overflow-y-auto"
                      >
                        {Array.from({ length: 5 }, (_, i) => {
                          const year = new Date().getFullYear() - i;
                          return (
                            <SelectItem key={year} value={year.toString()}>
                              {year}
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex flex-col h-full">
                    <label className="block text-sm font-medium text-blue-800 mb-1">
                      Semana
                    </label>
                    <Select
                      value={selectedWeek.toString()}
                      onValueChange={(value) => {
                        const week = parseInt(value);
                        if (!isNaN(week)) {
                          handleWeekChange(week);
                        }
                      }}
                    >
                      <SelectTrigger className="w-full border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent h-10">
                        <SelectValue placeholder="Seleccione una semana" />
                      </SelectTrigger>
                      <SelectContent
                        position="popper"
                        side="bottom"
                        align="start"
                        sideOffset={4}
                        className="z-[9999] max-h-[200px] overflow-y-auto"
                      >
                        {Array.from({ length: 52 }, (_, i) => {
                          const week = i + 1;
                          return (
                            <SelectItem key={week} value={week.toString()}>
                              Semana {week}
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                {currentWeek && (
                  <div className="mt-2 flex items-center gap-2">
                    <span className="text-sm font-medium text-blue-800">Período:</span>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-blue-800 border border-blue-200">
                      {currentWeek.label}
                    </span>
                  </div>
                )}
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              {kpiFields.map((field) => (
                <div key={field.key} className="space-y-2">
                  <div className="space-y-1">
                    <Tooltip content={field.description}>
                      <h3 className="text-sm font-bold text-gray-600 uppercase tracking-wide cursor-default">
                        {field.title}
                      </h3>
                    </Tooltip>
                  </div>
                  <input
                    type="text"
                    inputMode="decimal"
                    pattern="[0-9]*\.?[0-9]*"
                    placeholder="Ingrese un valor"
                    value={formData[field.key]}
                    onChange={(e) => handleInputChange(field.key, e.target.value)}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 placeholder-gray-400"
                    required
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 px-6 py-3 bg-gray-50 flex-shrink-0 rounded-b-lg">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-lg hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{loading ? "Guardando..." : "Guardar"}</span>
            </button>
          </div>
        </form>
      </div>

      {/* Alert Dialog para KPI duplicado */}
      <AlertDialog
        isOpen={showDuplicateAlert}
        onClose={() => setShowDuplicateAlert(false)}
        title="Ya existe un KPI para esta semana"
        message={`Ya existe un registro de KPI para la semana ${formData.weekNumber}/${formData.year}. Por favor, selecciona una semana diferente o edita el registro existente.`}
        type="warning"
        buttonText="Entendido"
      />

      {/* Alert Dialog para restricción de semana */}
      <AlertDialog
        isOpen={showWeekRestrictionAlert}
        onClose={() => setShowWeekRestrictionAlert(false)}
        title="No se pueden agregar datos para esta semana"
        message={weekRestrictionMessage}
        type="warning"
        buttonText="Entendido"
      />
    </div>
  );
};

export default KpiSemanalModal;
