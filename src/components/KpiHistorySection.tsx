"use client";

import React, { useState, useMemo, useCallback, memo } from "react";
import {
  Search,
  ArrowUp,
  ArrowDown,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  BarChart3
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import ConfirmDialog from "@/components/ui/ConfirmDialog";

interface KpiData {
  id: string;
  year: number;
  weekNumber: number;
  weekStartDate: Date | string;
  weekEndDate: Date | string;
  volumenTotalLitros: number;
  crecimientoMensual: number;
  margenBrutoPorLitro: number;
  tasaRetencionClientes: number;
  cumplimientoObjetivo: number;
  desviacionVentas: number;
  cicloPromedioCierre: number;
  clientesActivosMensuales: number;
  user?: {
    id: string;
    name?: string | null;
    email?: string | null;
  };
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  role?: string;
}

interface KpiHistorySectionProps {
  kpisSemanales: KpiData[];
  loadingKpis: boolean;
  user: User;
  onEditKpi: (kpi: KpiData) => void;
  onDeleteKpi: (kpiId: string) => Promise<void>;
  onRefresh: () => Promise<void>;
  showAnalytics?: boolean;
  showFilters?: boolean;
  selectedKpis?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
}

// Componente Tooltip personalizado estilo Chart.js
const Tooltip: React.FC<{ children: React.ReactNode; content: string; className?: string }> = ({
  children,
  content,
  className = ""
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: true, left: false, right: false, center: true });
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const updatePosition = React.useCallback(() => {
    if (!containerRef.current || !tooltipRef.current) return;

    const container = containerRef.current.getBoundingClientRect();
    const tooltip = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Determinar posición vertical (arriba o abajo)
    const spaceBelow = viewport.height - container.bottom;
    const spaceAbove = container.top;
    const tooltipHeight = tooltip.height || 60; // altura estimada

    const showAbove = spaceBelow < tooltipHeight && spaceAbove > tooltipHeight;

    // Determinar posición horizontal (centrado, izquierda o derecha)
    const containerCenter = container.left + container.width / 2;
    const tooltipWidth = tooltip.width || 200; // ancho estimado
    const halfTooltipWidth = tooltipWidth / 2;

    let horizontalPosition = 'center';
    if (containerCenter - halfTooltipWidth < 10) {
      horizontalPosition = 'left';
    } else if (containerCenter + halfTooltipWidth > viewport.width - 10) {
      horizontalPosition = 'right';
    }

    setPosition({
      top: !showAbove,
      left: horizontalPosition === 'left',
      right: horizontalPosition === 'right',
      center: horizontalPosition === 'center'
    });
  }, []);

  React.useEffect(() => {
    if (isVisible) {
      updatePosition();
    }
  }, [isVisible, updatePosition]);

  const getTooltipClasses = () => {
    let classes = "absolute px-3 py-2 text-xs font-medium text-white rounded-md shadow-lg whitespace-nowrap pointer-events-none transition-opacity duration-200 max-w-xs";

    if (position.top) {
      classes += " top-full mt-2";
    } else {
      classes += " bottom-full mb-2";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-0";
    } else if (position.right) {
      classes += " right-0";
    }

    return classes;
  };

  const getArrowClasses = () => {
    let classes = "absolute w-0 h-0";

    if (position.top) {
      classes += " bottom-full";
    } else {
      classes += " top-full";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-3";
    } else if (position.right) {
      classes += " right-3";
    }

    return classes;
  };

  const getArrowStyle = () => {
    const baseStyle = {
      borderLeft: '6px solid transparent',
      borderRight: '6px solid transparent',
    };

    if (position.top) {
      return {
        ...baseStyle,
        borderBottom: '6px solid rgba(0, 0, 0, 0.8)'
      };
    } else {
      return {
        ...baseStyle,
        borderTop: '6px solid rgba(0, 0, 0, 0.8)'
      };
    }
  };

  return (
    <div
      ref={containerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(4px)',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}
        >
          {content}
          <div
            className={getArrowClasses()}
            style={getArrowStyle()}
          ></div>
        </div>
      )}
    </div>
  );
};

const KpiHistorySection: React.FC<KpiHistorySectionProps> = ({
  kpisSemanales,
  loadingKpis,
  user,
  onEditKpi,
  onDeleteKpi,
  onRefresh,
  showAnalytics: externalShowAnalytics = false,
  showFilters: externalShowFilters = false,
  selectedKpis: externalSelectedKpis = [],
  onSelectionChange
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedYear, setSelectedYear] = useState<number | "all">("all");
  const [sortField, setSortField] = useState<keyof KpiData>("weekNumber");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [selectedKpis, setSelectedKpis] = useState<string[]>(externalSelectedKpis);

  // Estados para el dialog de confirmación
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    loading: false
  });

  // Usar los props externos si están disponibles, sino usar estados internos
  const showFilters = externalShowFilters;
  const showAnalytics = externalShowAnalytics;

  // Obtener años únicos para el filtro (incluye 2024 y 2025 como mínimo)
  const availableYears = useMemo(() => {
    const dataYears = [...new Set(kpisSemanales.map(kpi => kpi.year))];
    const allYears = [...new Set([...dataYears, 2024, 2025])].sort((a, b) => b - a);
    return allYears;
  }, [kpisSemanales]);



  // Filtrar y ordenar datos
  const filteredAndSortedKpis = useMemo(() => {
    let filtered = kpisSemanales.filter(kpi => {
      // Filtro de búsqueda: busca en semana/año y nombre/email del usuario
      const matchesSearch = searchTerm === "" ||
        `${kpi.weekNumber}/${kpi.year}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `semana ${kpi.weekNumber}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (kpi.user?.name?.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (kpi.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()));

      // Filtro por año
      const matchesYear = selectedYear === "all" || kpi.year === selectedYear;

      return matchesSearch && matchesYear;
    });

    // Ordenar
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];
      
      if (typeof aValue === 'string') aValue = aValue.toLowerCase();
      if (typeof bValue === 'string') bValue = bValue.toLowerCase();
      
      if (sortDirection === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [kpisSemanales, searchTerm, selectedYear, sortField, sortDirection]);

  // Resetear página y selecciones cuando cambian los filtros
  React.useEffect(() => {
    setCurrentPage(1);
    setSelectedKpis([]); // Limpiar selecciones cuando cambian los filtros
    onSelectionChange?.([]); // Notificar al componente padre
  }, [searchTerm, selectedYear, onSelectionChange]);

  // Paginación
  const totalPages = Math.ceil(filteredAndSortedKpis.length / itemsPerPage);
  const paginatedKpis = filteredAndSortedKpis.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleSort = useCallback((field: keyof KpiData) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  }, [sortField, sortDirection]);

  const handleSelectKpi = useCallback((kpiId: string) => {
    const newSelection = selectedKpis.includes(kpiId)
      ? selectedKpis.filter(id => id !== kpiId)
      : [...selectedKpis, kpiId];

    setSelectedKpis(newSelection);
    onSelectionChange?.(newSelection);
  }, [selectedKpis, onSelectionChange]);

  const handleSelectAll = useCallback(() => {
    const currentPageIds = paginatedKpis.map(kpi => kpi.id);
    const allCurrentPageSelected = currentPageIds.every(id => selectedKpis.includes(id));

    let newSelection;
    if (allCurrentPageSelected) {
      // Deseleccionar todos los de la página actual
      newSelection = selectedKpis.filter(id => !currentPageIds.includes(id));
    } else {
      // Seleccionar todos los de la página actual
      newSelection = [...new Set([...selectedKpis, ...currentPageIds])];
    }

    setSelectedKpis(newSelection);
    onSelectionChange?.(newSelection);
  }, [selectedKpis, paginatedKpis, onSelectionChange]);

  // Función para manejar la confirmación de eliminación
  const handleDeleteConfirmation = useCallback((kpi: KpiData) => {
    setConfirmDialog({
      isOpen: true,
      title: 'Eliminar KPI',
      message: `¿Estás seguro de que quieres eliminar el KPI de la semana ${kpi.weekNumber}/${kpi.year}? Esta acción no se puede deshacer.`,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }));

        try {
          await onDeleteKpi(kpi.id);
          setConfirmDialog(prev => ({ ...prev, isOpen: false, loading: false }));
        } catch (error) {
          console.error('Error al eliminar KPI:', error);
          setConfirmDialog(prev => ({ ...prev, loading: false }));
        }
      },
      loading: false
    });
  }, [onDeleteKpi]);



  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit'
    });
  };

  const getTrendIcon = (value: number, isPositiveGood: boolean = true) => {
    const isPositive = value >= 0;
    const isGood = isPositiveGood ? isPositive : !isPositive;

    return isPositive ? (
      <ArrowUp className={`h-3 w-3 ${isGood ? 'text-primary' : 'text-red-500'}`} style={isGood ? { color: 'rgb(22 163 74 / var(--tw-text-opacity, 1))' } : {}} />
    ) : (
      <ArrowDown className={`h-3 w-3 ${isGood ? 'text-primary' : 'text-red-500'}`} style={isGood ? { color: 'rgb(22 163 74 / var(--tw-text-opacity, 1))' } : {}} />
    );
  };

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return 'text-xs md:text-xs lg:text-sm bg-green-500/10 rounded-full px-3 py-1';
    if (value >= thresholds.warning) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };



  // Componente de fila de tabla memoizado para mejor rendimiento
  const TableRow = memo(({
    kpi,
    isSelected,
    onSelect,
    onEdit,
    onDelete
  }: {
    kpi: KpiData;
    isSelected: boolean;
    onSelect: (id: string) => void;
    onEdit: (kpi: KpiData) => void;
    onDelete: (id: string) => void;
  }) => (
    <tr className="bg-white hover:shadow-md shadow transition-all duration-200 rounded-xl border border-gray-200">
      <td className="px-4 py-3 rounded-l-xl">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={() => onSelect(kpi.id)}
          className="w-4 h-4 rounded-xl border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
        />
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="flex items-center space-x-3">
          <div>
            <div className="text-sm font-medium text-gray-900">
              Semana {kpi.weekNumber}/{kpi.year}
            </div>
            <div className="text-xs text-gray-500">
              {formatDate(kpi.weekStartDate)} - {formatDate(kpi.weekEndDate)}
            </div>
          </div>
        </div>
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-900">
          {kpi.volumenTotalLitros.toLocaleString()} L
        </div>
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-center">
        <span
          className={`text-sm font-medium ${kpi.crecimientoMensual >= 0 ? 'text-green-600' : 'text-red-600'}`}
        >
          {kpi.crecimientoMensual >= 0 ? '+' : ''}{kpi.crecimientoMensual.toFixed(2)}%
        </span>
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-900">
          ${kpi.margenBrutoPorLitro.toFixed(2)}
        </div>
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-900">
          {kpi.tasaRetencionClientes}%
        </div>
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="w-full">
          <div className="flex justify-between items-center">
            <div className="flex w-full h-2 bg-gray-200 rounded-full overflow-hidden mr-3">
              <div
                className="flex flex-col justify-center overflow-hidden rounded-full transition-all duration-300"
                role="progressbar"
                aria-valuenow={kpi.cumplimientoObjetivo}
                aria-valuemin={0}
                aria-valuemax={100}
                style={{
                  width: `${Math.min(kpi.cumplimientoObjetivo, 100)}%`,
                  backgroundColor: kpi.cumplimientoObjetivo >= 95 ? 'rgb(22 163 74)' :
                                  kpi.cumplimientoObjetivo >= 80 ? 'rgb(249 115 22)' : '#dc2626'
                }}
              ></div>
            </div>
            <span
              className="text-sm font-medium whitespace-nowrap"
              style={kpi.cumplimientoObjetivo >= 95 ? { color: 'rgb(22 163 74 / var(--tw-text-opacity, 1))' } :
                     kpi.cumplimientoObjetivo >= 80 ? { color: 'rgb(249 115 22 / var(--tw-text-opacity, 1))' } : { color: '#dc2626' }}
            >
              {kpi.cumplimientoObjetivo}%
            </span>
          </div>
        </div>
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-center">
        <span
          className={`text-sm font-medium ${kpi.desviacionVentas < 0 ? 'text-red-600' : ''}`}
          style={kpi.desviacionVentas >= 0 ? { color: 'rgb(22 163 74 / var(--tw-text-opacity, 1))' } : {}}
        >
          {kpi.desviacionVentas >= 0 ? '+' : ''}{kpi.desviacionVentas.toFixed(2)}%
        </span>
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-900">
          {kpi.cicloPromedioCierre} días
        </div>
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-900">
          {kpi.clientesActivosMensuales.toLocaleString()}
        </div>
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="text-sm text-gray-900">
          {kpi.user?.name || kpi.user?.email || 'Usuario desconocido'}
        </div>
        <div className="text-xs text-gray-500">
          {kpi.createdAt && new Date(kpi.createdAt).toLocaleDateString('es-ES')}
        </div>
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-right rounded-r-lg">
        <div className="flex items-center justify-end space-x-1">
          {/* Mostrar iconos para ADMIN y SUPER_ADMIN con funcionalidad */}
          {(user.role === "ADMIN" || user.role === "SUPER_ADMIN") && (
            <>
              <button
                onClick={() => onEdit(kpi)}
                className="p-1.5 text-gray-400 hover:text-primary hover:bg-primary/10 rounded transition-colors"
                title="Editar KPI"
              >
                <Edit className="h-4 w-4" />
              </button>
              <button
                onClick={() => handleDeleteConfirmation(kpi)}
                className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                title="Eliminar KPI"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </>
          )}
          {/* Mostrar iconos para WORKER pero deshabilitados */}
          {user.role === "WORKER" && (
            <>
              <span
                className="p-1.5 text-gray-300 cursor-not-allowed rounded"
                title="Solo administradores pueden editar"
              >
                <Edit className="h-4 w-4" />
              </span>
              <span
                className="p-1.5 text-gray-300 cursor-not-allowed rounded"
                title="Solo administradores pueden eliminar"
              >
                <Trash2 className="h-4 w-4" />
              </span>
            </>
          )}
        </div>
      </td>
    </tr>
  ));





  // Calcular analytics
  const analytics = useMemo(() => {
    if (filteredAndSortedKpis.length === 0) return null;

    const totalKpis = filteredAndSortedKpis.length;
    const avgCumplimiento = filteredAndSortedKpis.reduce((sum, kpi) => sum + kpi.cumplimientoObjetivo, 0) / totalKpis;
    const avgCrecimiento = filteredAndSortedKpis.reduce((sum, kpi) => sum + kpi.crecimientoMensual, 0) / totalKpis;
    const avgMargen = filteredAndSortedKpis.reduce((sum, kpi) => sum + kpi.margenBrutoPorLitro, 0) / totalKpis;
    const avgRetencion = filteredAndSortedKpis.reduce((sum, kpi) => sum + kpi.tasaRetencionClientes, 0) / totalKpis;

    const excelentWeeks = filteredAndSortedKpis.filter(kpi => kpi.cumplimientoObjetivo >= 95).length;
    const goodWeeks = filteredAndSortedKpis.filter(kpi => kpi.cumplimientoObjetivo >= 80 && kpi.cumplimientoObjetivo < 95).length;
    const poorWeeks = filteredAndSortedKpis.filter(kpi => kpi.cumplimientoObjetivo < 80).length;

    const bestWeek = filteredAndSortedKpis.reduce((best, current) =>
      current.cumplimientoObjetivo > best.cumplimientoObjetivo ? current : best
    );

    const worstWeek = filteredAndSortedKpis.reduce((worst, current) =>
      current.cumplimientoObjetivo < worst.cumplimientoObjetivo ? current : worst
    );

    return {
      totalKpis,
      avgCumplimiento,
      avgCrecimiento,
      avgMargen,
      avgRetencion,
      excelentWeeks,
      goodWeeks,
      poorWeeks,
      bestWeek,
      worstWeek
    };
  }, [filteredAndSortedKpis]);

  if (loadingKpis) {
    return (
      <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-gray-600">Cargando historial de KPIs...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-hidden">

      {/* Filters */}
      {showFilters && (
        <div className="pt-3 pb-3 px-2">
          <div className="flex ">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl w-full">
              {/* Campo de búsqueda - ocupa 2 columnas */}
              <div className="flex flex-col h-full md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Buscar
                </label>
                <div className="relative w-full flex-grow">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Buscar por semana.."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent h-10"
                  />
                </div>
              </div>
              
              {/* Selector de año - ocupa 1 columna */}
              <div className="flex flex-col h-full">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Año
                </label>
                <Select
                  value={selectedYear.toString()}
                  onValueChange={(value) => setSelectedYear(value === "all" ? "all" : parseInt(value))}
                >
                  <SelectTrigger className="w-full border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent h-10">
                    <SelectValue placeholder="Seleccione un año" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos los años</SelectItem>
                    {availableYears.map(year => (
                      <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* Botón de limpiar - ocupa 1 columna */}
              <div className="flex flex-col h-full justify-end">
                <button
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedYear("all");
                    setCurrentPage(1);
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors h-10"
                >
                  Limpiar filtros
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Analytics Dashboard */}
      {analytics && showAnalytics && (
        <div className="py-4">
          {/* Primero: Cards de Mejor y Peor Semana */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="bg-white rounded-lg p-3 shadow-md" title="Semana con el mayor porcentaje de cumplimiento del objetivo de ventas">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-gray-900">Mejor Semana</div>
                  <div className="text-xs text-gray-500">
                    Semana {analytics.bestWeek.weekNumber}/{analytics.bestWeek.year}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold" style={{ color: 'rgb(22 163 74 / var(--tw-text-opacity, 1))' }}>
                    {analytics.bestWeek.cumplimientoObjetivo}%
                  </div>
                  <div className="text-xs text-gray-500">Cumplimiento del Objetivo de Ventas</div>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-lg p-3 shadow-md" title="Semana con el menor porcentaje de cumplimiento del objetivo de ventas">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-gray-900">Semana a Mejorar</div>
                  <div className="text-xs text-gray-500">
                    Semana {analytics.worstWeek.weekNumber}/{analytics.worstWeek.year}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-red-600">
                    {analytics.worstWeek.cumplimientoObjetivo}%
                  </div>
                  <div className="text-xs text-gray-500">Cumplimiento del Objetivo de Ventas</div>
                </div>
              </div>
            </div>
          </div>

          {/* Segundo: Estadísticas Promedio */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 pt-4 border-t border-gray-200">
            <div className="text-center" title="Promedio del porcentaje de cumplimiento del objetivo de ventas de todas las semanas">
              <div className="text-lg font-semibold text-gray-900">
                {analytics.avgCumplimiento.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Cumplimiento del Objetivo de Ventas Promedio</div>
            </div>
            <div className="text-center" title="Promedio del porcentaje de crecimiento de ventas respecto al período anterior">
              <div className="text-lg font-semibold text-gray-900">
                {analytics.avgCrecimiento >= 0 ? '+' : ''}{analytics.avgCrecimiento.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Crecimiento de Ventas Promedio</div>
            </div>
            <div className="text-center" title="Promedio del margen bruto obtenido por cada litro vendido">
              <div className="text-lg font-semibold text-gray-900">
                ${analytics.avgMargen.toFixed(2)}
              </div>
              <div className="text-xs text-gray-500">Margen Bruto por Litro Promedio</div>
            </div>
            <div className="text-center" title="Promedio del porcentaje de clientes que se mantienen activos respecto al período anterior">
              <div className="text-lg font-semibold text-gray-900">
                {analytics.avgRetencion.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Tasa de Retención de Clientes Promedio</div>
            </div>
            <div className="text-center" title="Número de semanas donde el cumplimiento del objetivo fue igual o superior al 95%">
              <div className="text-lg font-semibold" style={{ color: 'rgb(22 163 74 / var(--tw-text-opacity, 1))' }}>
                {analytics.excelentWeeks}
              </div>
              <div className="text-xs text-gray-500">Semanas con Cumplimiento Excelente (≥95%)</div>
            </div>
            <div className="text-center" title="Número de semanas donde el cumplimiento del objetivo fue inferior al 80%">
              <div className="text-lg font-semibold text-red-600">
                {analytics.poorWeeks}
              </div>
              <div className="text-xs text-gray-500">Semanas que Necesitan Mejora (&lt;80%)</div>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      {kpisSemanales.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-3">
            <BarChart3 className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay KPIs registrados</h3>
          <p className="text-gray-500 mb-2">Comienza agregando datos semanales para ver el historial</p>
        </div>
      ) : filteredAndSortedKpis.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron resultados</h3>
          <p className="text-gray-500 mb-4">Intenta ajustar los filtros de búsqueda</p>
          <button
            onClick={() => {
              setSearchTerm("");
              setSelectedYear("all");
              setCurrentPage(1);
            }}
            className="text-primary hover:text-primary/80 font-medium"
          >
            Limpiar filtros
          </button>
        </div>
      ) : (
        <>
          {/* Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full border-separate border-spacing-y-2">
              <thead>
                <tr>
                  <th className="px-4 py-1 text-left bg-transparent">
                    <input
                      type="checkbox"
                      checked={paginatedKpis.length > 0 && paginatedKpis.every(kpi => selectedKpis.includes(kpi.id))}
                      ref={(input) => {
                        if (input) {
                          const currentPageIds = paginatedKpis.map(kpi => kpi.id);
                          const selectedCurrentPage = currentPageIds.filter(id => selectedKpis.includes(id));
                          input.indeterminate = selectedCurrentPage.length > 0 && selectedCurrentPage.length < currentPageIds.length;
                        }
                      }}
                      onChange={handleSelectAll}
                      className="w-4 h-4 rounded-xl border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
                    />
                  </th>
                  <th
                    className="px-4 py-1 text-left text-sm font-normal text-gray-400 cursor-pointer hover:text-gray-600 bg-transparent"
                    onClick={() => handleSort('weekNumber')}
                  >
                    <Tooltip content="Semana del Año">
                      <div className="flex items-center space-x-1">
                        <span>Semana</span>
                        {sortField === 'weekNumber' && (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3 text-primary" /> : <ArrowDown className="h-3 w-3 text-primary" />
                        )}
                      </div>
                    </Tooltip>
                  </th>
                  <th
                    className="px-4 py-1 text-left text-sm font-normal text-gray-400 cursor-pointer hover:text-gray-600 bg-transparent"
                    onClick={() => handleSort('volumenTotalLitros')}
                  >
                    <Tooltip content="Volumen Total de Venta (Litros)">
                      <div className="flex items-center space-x-1">
                        <span>Volumen (L)</span>
                        {sortField === 'volumenTotalLitros' && (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3 text-primary" /> : <ArrowDown className="h-3 w-3 text-primary" />
                        )}
                      </div>
                    </Tooltip>
                  </th>
                  <th
                    className="px-4 py-1 text-left text-sm font-normal text-gray-400 cursor-pointer hover:text-gray-600 bg-transparent"
                    onClick={() => handleSort('crecimientoMensual')}
                  >
                    <Tooltip content="Crecimiento de Ventas (%)">
                      <div className="flex items-center space-x-1">
                        <span>Crecimiento (%)</span>
                        {sortField === 'crecimientoMensual' && (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3 text-primary" /> : <ArrowDown className="h-3 w-3 text-primary" />
                        )}
                      </div>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Margen Bruto por Litro Vendido">
                      <span>Margen Bruto</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Tasa de Retención de Clientes (%)">
                      <span>Retención (%)</span>
                    </Tooltip>
                  </th>
                  <th
                    className="px-4 py-1 text-left text-sm font-normal text-gray-400 cursor-pointer hover:text-gray-600 bg-transparent"
                    onClick={() => handleSort('cumplimientoObjetivo')}
                  >
                    <Tooltip content="Cumplimiento del Objetivo de Ventas (%)">
                      <div className="flex items-center space-x-1">
                        <span>Cumplimiento (%)</span>
                        {sortField === 'cumplimientoObjetivo' && (
                          sortDirection === 'asc' ? <ArrowUp className="h-3 w-3 text-primary" /> : <ArrowDown className="h-3 w-3 text-primary" />
                        )}
                      </div>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Desviación de Ventas Respecto al Presupuesto (%)">
                      <span>Desviación (%)</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Ciclo Promedio de Cierre de Ventas (días)">
                      <span>Ciclo Cierre</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Número de Clientes Activos">
                      <span>Clientes Activos</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-left text-sm font-normal text-gray-400 bg-transparent">
                    <Tooltip content="Usuario que registró los datos">
                      <span>Usuario</span>
                    </Tooltip>
                  </th>
                  <th className="px-4 py-1 text-right text-sm font-normal text-gray-400 bg-transparent">
                    <span>Acciones</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedKpis.map((kpi) => (
                  <TableRow
                    key={kpi.id}
                    kpi={kpi}
                    isSelected={selectedKpis.includes(kpi.id)}
                    onSelect={handleSelectKpi}
                    onEdit={onEditKpi}
                    onDelete={() => handleDeleteConfirmation(kpi)}
                  />
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Mostrando {((currentPage - 1) * itemsPerPage) + 1} a {Math.min(currentPage * itemsPerPage, filteredAndSortedKpis.length)} de {filteredAndSortedKpis.length} registros
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </button>
                  <span className="text-sm text-gray-700">
                    Página {currentPage} de {totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Dialog de confirmación para eliminar */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
        confirmText="Eliminar"
        cancelText="Cancelar"
        type="danger"
        loading={confirmDialog.loading}
      />
    </div>
  );
};

export default KpiHistorySection;
