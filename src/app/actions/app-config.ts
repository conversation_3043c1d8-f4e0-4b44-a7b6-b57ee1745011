"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/authOptions";
import { PrismaClient } from "@prisma/client";
import { z } from "zod";
import { revalidatePath } from "next/cache";

// Crear instancia de Prisma específica para este archivo
const prisma = new PrismaClient();

// Esquema de validación para configuración
const configSchema = z.object({
  key: z.string().min(1, "La clave es requerida"),
  value: z.string().min(1, "El valor es requerido"),
  description: z.string().optional()
});

export interface AppConfigData {
  id?: string;
  key: string;
  value: string;
  description?: string;
}

// Obtener configuración por clave (disponible para todos los usuarios)
export async function getAppConfig(key: string) {
  try {
    const config = await prisma.appConfig.findUnique({
      where: { key },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return { success: true, data: config };
  } catch (error) {
    console.error("Error al obtener configuración:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error interno del servidor"
    };
  }
}

// Crear o actualizar configuración
export async function setAppConfig(data: AppConfigData) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return { success: false, error: "No autorizado" };
    }

    // Solo administradores pueden modificar configuraciones
    if (session.user.role !== "ADMIN" && session.user.role !== "SUPER_ADMIN") {
      return { success: false, error: "Permisos insuficientes" };
    }

    // Validar datos
    const validatedData = configSchema.parse(data);

    // Verificar si la configuración ya existe
    const existingConfig = await prisma.appConfig.findUnique({
      where: { key: validatedData.key }
    });

    let config;
    if (existingConfig) {
      // Actualizar configuración existente
      config = await prisma.appConfig.update({
        where: { key: validatedData.key },
        data: {
          value: validatedData.value,
          description: validatedData.description,
          userId: session.user.id
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });
    } else {
      // Crear nueva configuración
      config = await prisma.appConfig.create({
        data: {
          key: validatedData.key,
          value: validatedData.value,
          description: validatedData.description,
          userId: session.user.id
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });
    }

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true, data: config };
  } catch (error) {
    console.error("Error al guardar configuración:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Datos inválidos",
        details: error.errors
      };
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Obtener todas las configuraciones
export async function getAllAppConfigs() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return { success: false, error: "No autorizado" };
    }

    // Solo administradores pueden ver todas las configuraciones
    if (session.user.role !== "ADMIN" && session.user.role !== "SUPER_ADMIN") {
      return { success: false, error: "Permisos insuficientes" };
    }

    const configs = await prisma.appConfig.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    return { success: true, data: configs };
  } catch (error) {
    console.error("Error al obtener configuraciones:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Eliminar configuración
export async function deleteAppConfig(key: string) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return { success: false, error: "No autorizado" };
    }

    // Solo administradores pueden eliminar configuraciones
    if (session.user.role !== "ADMIN" && session.user.role !== "SUPER_ADMIN") {
      return { success: false, error: "Permisos insuficientes" };
    }

    await prisma.appConfig.delete({
      where: { key }
    });

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true };
  } catch (error) {
    console.error("Error al eliminar configuración:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Funciones específicas para el tacómetro de proveedores (accesible para todos los usuarios)
export async function getTacometroMaxProveedores(): Promise<number> {
  try {
    const result = await getAppConfig("tacometro_max_proveedores");
    if (result.success && result.data) {
      return parseInt(result.data.value, 10);
    }

    // Si no existe configuración, crear una por defecto
    await initializeTacometroConfig();
    return 40; // Valor por defecto
  } catch (error) {
    console.error("Error al obtener configuración del tacómetro:", error);
    return 40; // Valor por defecto
  }
}

// Inicializar configuración por defecto del tacómetro
async function initializeTacometroConfig() {
  try {
    await prisma.appConfig.create({
      data: {
        key: "tacometro_max_proveedores",
        value: "40",
        description: "Valor máximo para el tacómetro de proveedores activos (nivel 'Excepcional')",
        userId: "system" // Usuario del sistema para configuraciones por defecto
      }
    });
  } catch (error) {
    // Si ya existe o hay error, no hacer nada
    console.log("Configuración del tacómetro ya existe o error al crear:", error);
  }
}

export async function setTacometroMaxProveedores(maxValue: number) {
  return await setAppConfig({
    key: "tacometro_max_proveedores",
    value: maxValue.toString(),
    description: "Valor máximo para el tacómetro de proveedores activos (nivel 'Excepcional')"
  });
}
